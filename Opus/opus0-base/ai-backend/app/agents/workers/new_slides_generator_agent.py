# app/agents/workers/new_slides_generator_agent.py
"""
Module: new_slides_generator_agent.py

This module defines the NewSlidesGeneratorAgent class, a worker agent responsible for generating
PowerPoint presentations (.pptx files) using incremental slide-by-slide creation. The new architecture:
Outline → Style Template → Base Presentation → Individual Slide Generation → Final Presentation.
It follows the system design specification for clean separation of content structure, visual styling,
and robust incremental implementation.
"""

import json
import re
import logging
import subprocess
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import (
    get_knowledge_base_dir,
    get_public_slides_dir,
    get_user_uploads_dir,
)
from app.utils.communication import send_message
from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_slides_examples_text, get_pptx_code_examples_text, get_slides_content_examples_text

from langchain_google_genai import ChatGoogleGenerative<PERSON><PERSON>

from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate
from langchain_core.output_parsers import StrOutputParser


# Initialize module-level logger
logger = logging.getLogger(__name__)


#──────────────────────────────────────────────────
#                         PROMPT TEMPLATES
#──────────────────────────────────────────────────


slides_outline_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.5,
    top_p=0.9,
)

slides_outline_prompt = ChatPromptTemplate.from_template(
    """
## Role
You're a professional presentation designer and content strategist. Generate a **comprehensive JSON outline** for a PowerPoint presentation that includes both structure AND detailed content for each slide.

---

## Objective
Create a complete presentation outline that defines slide structure, layout types, content themes, AND the actual detailed content (bullet points, text) for each slide. This will be used to generate python-pptx code.

---

## Input Context
**Subtask Description:** {subtask_desc}
**User Input:** {user_input}
**Dependencies Info:** {deps_info}

---

## Outline Structure Examples
{outline_examples}

---

## Content Examples (Use these as inspiration for detailed content)
{content_examples}

---

## Output Requirements
Generate a **valid JSON object** with this exact structure:

```json
{{
  "title": "Presentation Title",
  "subtitle": "Optional subtitle or tagline",
  "total_slides": 8,
  "slides": [
    {{
      "slide_number": 1,
      "layout_type": "title_slide",
      "title": "Slide Title",
      "content_type": "title_and_subtitle",
      "content_outline": "Brief description of slide content",
      "detailed_content": {{
        "bullet_points": [],
        "main_text": "Optional paragraph text for title slides"
      }}
    }},
    {{
      "slide_number": 2,
      "layout_type": "title_content",
      "title": "Slide Title",
      "content_type": "bullet_points",
      "content_outline": "Brief description of main points",
      "detailed_content": {{
        "bullet_points": [
          "Specific, actionable bullet point 1",
          "Specific, actionable bullet point 2",
          "Specific, actionable bullet point 3"
        ],
        "main_text": "Optional paragraph text for explanation"
      }}
    }}
  ]
}}
```

## Layout Types Available
- **title_slide**: Title and subtitle with optional background
- **title_content**: Title with bullet points or paragraphs
- **section_header**: Section divider with large title

## Content Types Available
- **title_and_subtitle**: For title slides
- **bullet_points**: For bulleted lists
- **section_break**: For section dividers

---

## Content Generation Guidelines

### For Bullet Points:
- Generate 3-6 specific, actionable bullet points per slide
- Include concrete data, examples, or insights (not generic placeholders)
- Make each point presentation-ready and engaging

### For Main Text:
- Provide optional paragraph text for slides needing explanation
- Keep concise but informative
- Use for title slides or slides requiring context

---

## Instructions
1. Analyze the user requirements and create an appropriate slide structure
2. Choose suitable layout types for each slide based on content needs
3. Generate detailed, specific content for each slide (not generic placeholders)
4. Ensure logical flow and progression through the presentation
5. Include 6-12 slides for optimal presentation length
6. Return **only** the JSON object, no additional text or formatting

**CRITICAL:** Generate actual detailed content, not placeholders like "List key points here" or "Add relevant statistics". Use the content examples as inspiration for the level of detail and specificity required.
"""
)


style_template_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.2,
    top_p=0.8,
)

style_template_prompt = ChatPromptTemplate.from_template(
    """
## Role
You're a modern PowerPoint design expert specializing in creating visually striking, contemporary presentations. Generate a JSON style template that defines bold, engaging visual styling based on the outline and user requirements.

---

## Objective
Create a comprehensive style template that will guide python-pptx code generation for visually impressive, modern presentation styling. When users request "cool looking", "visually appealing", or "modern" slides, implement advanced visual elements like gradients, accent bars, and creative color schemes.

---

## Input Context
**Outline:** {outline_json}
**User Input:** {user_input}
**Subtask Description:** {subtask_desc}

---

## Output Requirements
Generate a **valid JSON object** with this exact structure:

```json
{{
  "title": "Presentation Title",
  "global_theme": {{
    "primary_color": "#2E86AB",
    "secondary_color": "#A23B72",
    "accent_color": "#F18F01",
    "title_font": "Calibri",
    "body_font": "Calibri",
    "title_size": 36,
    "body_size": 18,
    "background_color": "#F5F5F5",
    "use_gradients": true,
    "use_accent_bars": true,
    "visual_style": "modern"
  }},
  "slides": [
    {{
      "slide_number": 1,
      "layout_type": "title_slide",
      "styling": {{
        "title_color": "#2E86AB",
        "subtitle_color": "#666666",
        "title_size": 44,
        "subtitle_size": 24,
        "background_color": "#F5F5F5",
        "use_gradient_header": true,
        "use_footer_accent": true,
        "accent_color": "#F18F01"
      }}
    }}
  ]
}}
```

## Advanced Visual Elements to Consider
- **Gradient backgrounds**: Use gradient fills for headers, banners, or full backgrounds
- **Accent bars**: Add footer or header accent bars for visual impact
- **Creative color schemes**: Bold, modern color combinations that match the topic
- **Visual hierarchy**: Strong contrast between elements for better readability
- **Modern typography**: Contemporary font choices that enhance the presentation theme

## Enhanced Color Guidelines
- **Bold & Modern**: Use vibrant, contemporary color schemes that create visual impact
- **Topic-Appropriate**: Match colors to presentation theme (tech=blues/greens, business=blues/grays, creative=bold combinations)
- **High Contrast**: Ensure excellent readability with strong color contrasts
- **Gradient-Ready**: Choose colors that work well in gradient combinations
- **Accent Integration**: Use accent colors for highlights, bars, and emphasis elements

## Advanced Styling Guidelines
- **Gradients**: Implement gradient backgrounds, headers, or accent elements when appropriate
- **Accent Bars**: Add footer or header accent bars for professional visual appeal
- **Modern Fonts**: Use contemporary, readable fonts that enhance the presentation theme
- **Visual Hierarchy**: Create clear visual hierarchy through color, size, and positioning

---

## Instructions
1. **Analyze user intent**: Pay special attention to requests for "cool", "modern", "visually appealing", or "professional" styling
2. **Choose bold colors**: Select vibrant, modern color schemes that create visual impact while maintaining readability
3. **Enable advanced features**: Set flags for gradients, accent bars, and other visual enhancements when appropriate
4. **Match the topic**: Align visual style with presentation content (tech, business, creative, etc.)
5. **Ensure consistency**: Maintain cohesive styling across all slides while allowing for visual variety
6. **Return JSON only**: Provide ONLY the JSON object, no additional text, explanations, or formatting

**CRITICAL**: When users request visually appealing or cool-looking slides, be BOLD and CREATIVE with your color choices and visual elements while maintaining professionalism.

**IMPORTANT**: Your response must contain ONLY valid JSON. Do not include any explanatory text before or after the JSON object.
"""
)


# Model for individual slide code generation
slide_code_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.3,
    top_p=0.9,
)

# Focused prompt for individual slide generation
slide_code_prompt = ChatPromptTemplate.from_template(
    """
## Role
You are a senior python developer and PowerPoint design expert who creates visually appealing presentations using python-pptx. Generate code to add ONE visually impressive slide to an existing PowerPoint presentation.

## Critical Requirements
1. Load existing presentation from specified path
2. Add exactly ONE slide with specified content and styling
3. Save presentation back to SAME path
4. Use proven python-pptx patterns shown in the examples as inspiration, but feel free to enhance and combine techniques for better visual impact
5. Return ONLY executable Python code, no markdown formatting
6. Ensure that the code that you output is formatted correctly, and suitable for direct execution (ensure that newlines are in the correct places, etc.)
7. ** IMPORTANT:** Produce production ready code with no comments, no logging, no debug code, etc. The code that you generate will be directly executed with no human intervention.
8. ** IMPORTANT: ** ENSURE THAT THERE IS A NEWLINE AFTER EACH VALID SINGLE PYTHON STATEMENT THAT YOU PRODUCE
9. Make sure that you use the font size specified in the slide styling information given.
10. **VISUAL ENHANCEMENT:** Create visually appealing slides using colors, gradients, accent elements, and professional layouts when appropriate

## Input Data
{slide_content}

{slide_styling}

**Presentation Path:** {presentation_path}

## Visual Examples (Use these as inspiration for creating impressive slides)
{examples}

## Layout Mapping
- title_slide: layout 0
- title_content: layout 1
- section_header: layout 2
- blank: layout 6

## Visual Enhancement Guidelines
- **Pay attention to styling flags**: When you see `use_gradients: true`, `use_accent_bars: true`, or `use_gradient_header: true` in the styling information, implement these visual enhancements
- **Visual Style**: Use the `visual_style` field (modern, professional, creative) to guide your design approach
- **Gradient Implementation**: When gradients are enabled, use gradient fills for backgrounds, headers, or accent elements
- **Accent Bars**: When accent bars are enabled, add footer or header accent bars using the specified accent color
- **Color Hierarchy**: Use primary, secondary, and accent colors from the global theme for strong visual impact
- **Modern Design**: Combine and enhance techniques from examples for maximum visual appeal

*EXTREMELY IMPORTANT: AFTER YOU GENERATE EVERY VALID SINGLE PYTHON STATEMENT, YOU MUST ADD A NEWLINE. DO NOT CONCATENATE MULTIPLE STATEMENTS ON THE SAME LINE. E.G. DO NOT DO THIS: from pptx.dml.color import RGBColorpresentation_path = ...*

Generate complete, executable Python code that creates visually impressive slides using the examples as inspiration.
"""
)

# Code fixing prompt for retry attempts
slide_code_fix_prompt = ChatPromptTemplate.from_template(
    """
## Role
You are a senior python developer who is an expert in creating PowerPoint presentations using python-pptx. Fix the provided Python code that failed to execute properly.

## Critical Requirements
1. Analyze the error message and fix the specific issues
2. Return ONLY executable Python code, no markdown formatting
3. Ensure proper formatting with newlines after each statement
4. Use only proven python-pptx patterns shown in the examples
5. Maintain the original intent of the code while fixing the errors

## Original Code That Failed
```python
{failed_code}
```

## Error Message
{error_message}

## Context
{slide_content}

{slide_styling}

**Presentation Path:** {presentation_path}

## Examples (Follow these examples closely)
{examples}

## Common Issues to Fix
- Syntax errors (missing colons, parentheses, quotes)
- Import statement formatting
- Multiple statements on same line
- Incorrect indentation
- Missing or incorrect variable assignments

*EXTREMELY IMPORTANT: AFTER YOU GENERATE EVERY VALID SINGLE PYTHON STATEMENT, YOU MUST ADD A NEWLINE. DO NOT CONCATENATE MULTIPLE STATEMENTS ON THE SAME LINE.*
*EXTREMELY IMPORTANT: DO NOT INTRODUCE ANY MORE ERRORS THAN THE ONES THAT ARE ALREADY PRESENT IN THE FAILED CODE. ONLY FIX THE ERRORS, DO NOT CHANGE THE FUNCTIONALITY OR INTENT OF THE CODE.*

Generate the corrected, complete, executable Python code:
"""
)

#──────────────────────────────────────────────────
#                         CLASS DEFINITION
#──────────────────────────────────────────────────


class NewSlidesGeneratorAgent(BaseAgent):
    """
    Worker agent capable of generating PowerPoint presentations using the new architecture.

    Inherits from BaseAgent and implements task-specific logic to:
      1. Parse the task details (subtask_id, subtask_description, user_message, deps_info).
      2. Generate a slides outline based on the subtask details (using LLM).
      3. Generate a style template based on the outline (using LLM).
      4. Generate python-pptx code using RAG examples (using LLM).
      5. Execute the generated code to create the PPTX file.
      6. Upload to R2 and provide download link.
      7. Report completion to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        super().__init__(chat_id, "new_slides_generator")
        self.chat_id = chat_id
        self.agent_id = agent_id
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.outline_plan = {}
        self.style_template = {}
        self.generated_code = ""
        self.uploaded_images = []

    async def _invoke_chain(
        self, chain: Any, payload: Dict[str, Any], *, stream: bool = True
    ) -> str:
        """Run ``chain`` with ``payload`` and return the combined result.

        When ``stream`` is ``True`` runtime log events are emitted for each
        chunk received from ``chain``.
        """
        self.logger.info(
            "[NewSlidesGenerator] LLM input payload:\n%s", json.dumps(payload, indent=2)
        )

        chunks: List[str] = []
        if stream:
            self.runtime_log_stream("stream")

        async for chunk in chain.astream(payload):
            text = chunk if isinstance(chunk, str) else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        New incremental execution flow:
        1) Generate a slides outline in JSON (raw).
        2) Generate a style template in JSON based on the outline.
        3) Create base presentation file.
        4) Generate and execute code for each slide individually.
        5) Upload to R2 and get presigned URL.
        6) Report completion to manager.
        """
        try:
            self.start_timer("new slides agent")

            # Extract task details
            subtask_id = task_details.get("subtask_id", "unknown")
            subtask_description = task_details.get("subtask_description", "")
            user_input = task_details.get("user_input", "")
            deps_info = task_details.get("deps_info", "")

            self.logger.info(
                f"[NewSlidesGenerator] Starting task execution for subtask_id='{subtask_id}'"
            )

            # ----------------------------
            # (1) Generate & Save Outline
            # ----------------------------
            self.runtime_log("Generating Presentation Outline")
            self.logger.info(
                f"[NewSlidesGenerator] Outline generation input => subtask_desc='{subtask_description}'"
            )

            outline_plan = await self.determine_slides_outline(
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )

            self.outline_plan = outline_plan
            self.logger.info("[NewSlidesGenerator] Outline generated successfully.")
            self.save_outline_to_md_file(subtask_id, outline_plan)

            # ----------------------------
            # (2) Generate & Save Style Template
            # ----------------------------
            self.runtime_log("Generating Style Template")
            style_template = await self.generate_style_template(
                outline_plan=outline_plan,
                user_input=user_input,
                subtask_desc=subtask_description,
            )

            self.style_template = style_template
            self.logger.info("[NewSlidesGenerator] Style template generated successfully.")
            self.save_style_template_to_md_file(subtask_id, style_template)

            # ----------------------------
            # (3) Create Presentation Incrementally
            # ----------------------------
            self.runtime_log("Creating PowerPoint Presentation")
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            pptx_name = f"pptx_result_{subtask_id}_{timestamp}.pptx"

            pptx_path = await self.create_presentation_incrementally(
                outline_plan=outline_plan,
                style_template=style_template,
                filename=pptx_name,
            )

            self.logger.info(f"[NewSlidesGenerator] PPTX created successfully: {pptx_path}")

            # ----------------------------
            # (5) Upload to R2 & Generate Link
            # ----------------------------
            self.runtime_log("Uploading Presentation")
            from app.utils.r2_client import upload_file_to_r2

            pptx_url = upload_file_to_r2(
                pptx_path,
                content_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
                expires_in=60 * 60 * 24,  # valid for 24 h
            )

            doc_url = f"{settings.BASE_URL}/docs/{pptx_path.name}"
            self.runtime_log(f"Generated PPTX: {doc_url}")

            # ----------------------------
            # (6) Create Results Summary
            # ----------------------------
            combined_markdown = (
                f"# Your PowerPoint presentation is ready\n\n"
                f"**Download PPTX:** [{pptx_name}]({doc_url})\n\n"
                f"---\n\n"
                f"## Presentation Details:\n"
                f"- **Title:** {outline_plan.get('title', 'Presentation')}\n"
                f"- **Total Slides:** {outline_plan.get('total_slides', 'N/A')}\n"
                f"- **Theme:** Professional styling applied\n\n"
                f"You can always download your presentation at {doc_url}\n\n"
                f"#IMPORTANT: Always give the pptx url link to the user in your final message."
            )

            combined_file_path = self.save_combined_results_to_md_file(
                subtask_id, combined_markdown
            )

            # ----------------------------
            # (7) Report Results
            # ----------------------------
            # Send the required message format for worker-to-manager communication
            send_message(
                chat_id=self.chat_id,
                sender="worker",
                receiver="manager",
                message={
                    "subtask_id": subtask_id,
                    "output_file": str(combined_file_path),
                },
            )

            self.stop_timer("new slides agent")
            self.logger.info(f"[NewSlidesGenerator] Task completed successfully for subtask_id='{subtask_id}'")

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Task execution failed: {e}")

            # Create an error output file
            try:
                knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
                knowledge_base_dir.mkdir(parents=True, exist_ok=True)
                timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
                error_file_path = knowledge_base_dir / f"slides_error_{subtask_id}_{timestamp}.md"

                error_content = f"# Slides Generation Error\n\n**Error:** {str(e)}\n\n**Subtask ID:** {subtask_id}\n"
                with open(error_file_path, "w", encoding="utf-8") as f:
                    f.write(error_content)

                send_message(
                    chat_id=self.chat_id,
                    sender="worker",
                    receiver="manager",
                    message={
                        "subtask_id": subtask_id,
                        "output_file": str(error_file_path),
                    },
                )
            except Exception as msg_error:
                self.logger.error(f"[NewSlidesGenerator] Failed to send error message: {msg_error}")

            raise

    #──────────────────────────────────────────────────
    #                         OUTLINE GENERATION
    #──────────────────────────────────────────────────

    async def determine_slides_outline(
        self, subtask_desc: str, user_input: str, deps_info: str
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive JSON outline with detailed content for the presentation using LLM.
        Now includes both structure and detailed content in a single call.
        """
        # Get relevant outline structure examples from RAG system
        outline_examples_text = get_slides_examples_text(user_input, k=2)

        # Get relevant detailed content examples from RAG system
        content_examples_text = get_slides_content_examples_text(user_input, k=3)

        input_data = {
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "outline_examples": outline_examples_text,
            "content_examples": content_examples_text
        }

        self.logger.info(f"[NewSlidesGenerator] Enhanced outline input tokens: {count_tokens(str(input_data))}")

        try:
            chain = slides_outline_prompt | slides_outline_model | StrOutputParser()
            outline_text = await self._invoke_chain(chain, input_data)

            self.logger.info(f"[NewSlidesGenerator] Raw enhanced outline response length: {len(outline_text)} chars")

            # Parse JSON from the response
            outline_plan = self._parse_json_response(outline_text, "enhanced_outline")

            # Validate enhanced outline structure
            self._validate_enhanced_outline(outline_plan)

            return outline_plan

        except Exception as e:
            self.logger.warning(f"[NewSlidesGenerator] Enhanced outline generation failed: {e}")
            # Fallback outline with basic detailed content
            return self._create_fallback_enhanced_outline(user_input)

    def _parse_json_response(self, response_text: str, response_type: str) -> Dict[str, Any]:
        """Parse JSON from LLM response, handling markdown code blocks."""
        try:
            # Remove markdown code blocks if present
            cleaned_text = response_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.startswith("```"):
                cleaned_text = cleaned_text[3:]
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]

            # Parse JSON
            parsed_json = json.loads(cleaned_text.strip())
            self.logger.info(f"[NewSlidesGenerator] Successfully parsed {response_type} JSON")
            return parsed_json

        except json.JSONDecodeError as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to parse {response_type} JSON: {e}")
            raise ValueError(f"Invalid JSON in {response_type} response")

    def _validate_enhanced_outline(self, outline: Dict[str, Any]) -> None:
        """Validate the enhanced outline structure includes detailed content."""
        required_fields = ["title", "slides"]
        for field in required_fields:
            if field not in outline:
                raise ValueError(f"Missing required field: {field}")

        slides = outline.get("slides", [])
        if not isinstance(slides, list) or len(slides) == 0:
            raise ValueError("Outline must contain at least one slide")

        for i, slide in enumerate(slides):
            slide_required = ["slide_number", "layout_type", "title", "content_type"]
            for field in slide_required:
                if field not in slide:
                    raise ValueError(f"Slide {i+1} missing required field: {field}")

            # Validate detailed content structure
            detailed_content = slide.get("detailed_content", {})
            if detailed_content:
                if "bullet_points" in detailed_content:
                    bullet_points = detailed_content["bullet_points"]
                    if not isinstance(bullet_points, list):
                        self.logger.warning(f"Slide {i+1} has invalid bullet_points structure")
                if "main_text" in detailed_content:
                    main_text = detailed_content["main_text"]
                    if not isinstance(main_text, str):
                        self.logger.warning(f"Slide {i+1} has invalid main_text structure")
            else:
                self.logger.warning(f"Slide {i+1} missing detailed_content - will use fallback")

        self.logger.info(f"[NewSlidesGenerator] Enhanced outline validation passed for {len(slides)} slides")

    def _create_fallback_enhanced_outline(self, user_input: str) -> Dict[str, Any]:
        """Create a fallback enhanced outline with basic detailed content."""
        return {
            "title": f"Presentation: {user_input[:50]}" if user_input else "Presentation",
            "subtitle": "Generated Presentation",
            "total_slides": 6,
            "slides": [
                {
                    "slide_number": 1,
                    "layout_type": "title_slide",
                    "title": f"Presentation: {user_input[:30]}" if user_input else "Presentation",
                    "content_type": "title_and_subtitle",
                    "content_outline": "Title slide with presentation topic",
                    "detailed_content": {
                        "bullet_points": [],
                        "main_text": f"Presentation about {user_input}" if user_input else "Generated Presentation"
                    }
                },
                {
                    "slide_number": 2,
                    "layout_type": "title_content",
                    "title": "Overview",
                    "content_type": "bullet_points",
                    "content_outline": "Presentation overview and agenda",
                    "detailed_content": {
                        "bullet_points": [
                            "Introduction to the topic",
                            "Key concepts and definitions",
                            "Main discussion points",
                            "Conclusions and next steps"
                        ],
                        "main_text": ""
                    }
                },
                {
                    "slide_number": 3,
                    "layout_type": "title_content",
                    "title": "Key Points",
                    "content_type": "bullet_points",
                    "content_outline": "Main content and discussion",
                    "detailed_content": {
                        "bullet_points": [
                            "First key point with supporting details",
                            "Second important concept to discuss",
                            "Third critical element for consideration"
                        ],
                        "main_text": ""
                    }
                },
                {
                    "slide_number": 4,
                    "layout_type": "title_content",
                    "title": "Analysis",
                    "content_type": "bullet_points",
                    "content_outline": "Detailed analysis and insights",
                    "detailed_content": {
                        "bullet_points": [
                            "Analysis of current situation",
                            "Identification of key trends",
                            "Assessment of opportunities and challenges"
                        ],
                        "main_text": ""
                    }
                },
                {
                    "slide_number": 5,
                    "layout_type": "title_content",
                    "title": "Conclusions",
                    "content_type": "bullet_points",
                    "content_outline": "Summary and next steps",
                    "detailed_content": {
                        "bullet_points": [
                            "Summary of key findings",
                            "Recommended actions",
                            "Next steps and follow-up"
                        ],
                        "main_text": ""
                    }
                },
                {
                    "slide_number": 6,
                    "layout_type": "section_header",
                    "title": "Thank You",
                    "content_type": "section_break",
                    "content_outline": "Closing slide",
                    "detailed_content": {
                        "bullet_points": [],
                        "main_text": "Questions and Discussion"
                    }
                }
            ]
        }

    #──────────────────────────────────────────────────
    #                         STYLE TEMPLATE GENERATION
    #──────────────────────────────────────────────────

    async def generate_style_template(
        self, outline_plan: Dict[str, Any], user_input: str, subtask_desc: str
    ) -> Dict[str, Any]:
        """
        Generate a JSON style template based on the outline.
        """
        outline_json = json.dumps(outline_plan, ensure_ascii=False, indent=2)

        input_data = {
            "outline_json": outline_json,
            "user_input": user_input,
            "subtask_desc": subtask_desc,
        }

        self.logger.info(f"[NewSlidesGenerator] Style template input tokens: {count_tokens(str(input_data))}")

        try:
            chain = style_template_prompt | style_template_model | StrOutputParser()
            template_text = await self._invoke_chain(chain, input_data)

            self.logger.info(f"[NewSlidesGenerator] Raw style template response length: {len(template_text)} chars")

            # Parse JSON from the response
            style_template = self._parse_json_response(template_text, "style template")

            # Validate style template structure
            self._validate_style_template(style_template)

            return style_template

        except Exception as e:
            self.logger.warning(f"[NewSlidesGenerator] Style template generation failed: {e}")
            # Fallback style template
            return self._create_fallback_style_template(outline_plan)

    def _validate_style_template(self, template: Dict[str, Any]):
        """Validate style template structure."""
        required_fields = ["title", "global_theme", "slides"]
        for field in required_fields:
            if field not in template:
                raise ValueError(f"Missing required field in style template: {field}")

        if "primary_color" not in template["global_theme"]:
            raise ValueError("Style template must include primary_color in global_theme")

    def _create_fallback_style_template(self, outline_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Create a basic fallback style template if generation fails."""
        slides_styling = []
        for slide in outline_plan.get("slides", []):
            slide_styling = {
                "slide_number": slide.get("slide_number", 1),
                "layout_type": slide.get("layout_type", "title_content"),
                "styling": {
                    "title_color": "#1f4e79",
                    "title_size": 36,
                    "background_color": "#ffffff"
                }
            }
            if slide.get("layout_type") == "title_slide":
                slide_styling["styling"].update({
                    "subtitle_color": "#666666",
                    "title_size": 44,
                    "subtitle_size": 24
                })
            slides_styling.append(slide_styling)

        return {
            "title": outline_plan.get("title", "Presentation"),
            "global_theme": {
                "primary_color": "#1f4e79",
                "secondary_color": "#ffffff",
                "accent_color": "#ff6b35",
                "title_font": "Calibri",
                "body_font": "Calibri",
                "title_size": 36,
                "body_size": 18,
                "background_color": "#ffffff"
            },
            "slides": slides_styling
        }

    #──────────────────────────────────────────────────
    #                         PYTHON-PPTX CODE GENERATION
    #──────────────────────────────────────────────────

    def _clean_python_code(self, code_text: str) -> str:
        match = re.search(r"```(?:python)?\n(.*?)```", code_text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return code_text.strip()

    def _validate_python_code(self, code: str):
        """Basic validation of the generated Python code."""
        required_imports = ["from pptx import Presentation"]
        required_functions = ["def create_presentation"]

        for import_stmt in required_imports:
            if import_stmt not in code:
                raise ValueError(f"Missing required import: {import_stmt}")

        for func in required_functions:
            if func not in code:
                raise ValueError(f"Missing required function: {func}")

    def _create_fallback_pptx_code(self, outline_plan: Dict[str, Any], style_template: Dict[str, Any]) -> str:
        """Create basic fallback python-pptx code if generation fails."""
        title = outline_plan.get("title", "Presentation")
        subtitle = outline_plan.get("subtitle", "")

        return f'''from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

def create_presentation(output_path):
    # Create presentation
    prs = Presentation()

    # Title slide
    slide = prs.slides.add_slide(prs.slide_layouts[0])
    title_shape = slide.shapes.title
    subtitle_shape = slide.placeholders[1]

    title_shape.text = "{title}"
    subtitle_shape.text = "{subtitle}"

    # Apply basic styling
    title_paragraph = title_shape.text_frame.paragraphs[0]
    title_paragraph.font.name = "Calibri"
    title_paragraph.font.size = Pt(44)
    title_paragraph.font.color.rgb = RGBColor(31, 78, 121)

    subtitle_paragraph = subtitle_shape.text_frame.paragraphs[0]
    subtitle_paragraph.font.name = "Calibri"
    subtitle_paragraph.font.size = Pt(24)
    subtitle_paragraph.font.color.rgb = RGBColor(102, 102, 102)

    # Save presentation
    prs.save(output_path)
    return output_path
'''

    #──────────────────────────────────────────────────
    #                    INCREMENTAL PRESENTATION CREATION
    #──────────────────────────────────────────────────

    async def create_presentation_incrementally(
        self, outline_plan: Dict[str, Any], style_template: Dict[str, Any], filename: str
    ) -> Path:
        """
        Create presentation slide by slide using incremental approach.
        """
        try:
            # Create output directory and path
            slides_dir = get_public_slides_dir(self.chat_id)
            slides_dir.mkdir(parents=True, exist_ok=True)
            output_path = slides_dir / filename

            self.logger.info(f"[NewSlidesGenerator] Creating presentation at: {output_path}")

            # Step 1: Create base presentation
            await self.create_base_presentation(output_path, style_template)
            self.runtime_log("Base presentation created")

            # Step 2: Add slides one by one
            outline_slides = outline_plan.get("slides", [])
            template_slides = style_template.get("slides", [])

            self.logger.info(f"[NewSlidesGenerator] Creating {len(outline_slides)} slides incrementally")

            for i in range(len(outline_slides)):
                slide_num = outline_slides[i].get("slide_number", i + 1)

                try:
                    self.runtime_log(f"Creating slide {slide_num}")
                    self.logger.info(f"[NewSlidesGenerator] Processing slide {slide_num} (index {i})")

                    # Generate and execute code for this specific slide (includes retry logic)
                    await self.generate_slide_code(
                        slide_index=i,
                        outline_plan=outline_plan,
                        style_template=style_template,
                        presentation_path=output_path
                    )

                    # Validate slide was added successfully
                    if await self.validate_slide_created(output_path, slide_num):
                        self.runtime_log(f"Slide {slide_num} created successfully")
                        self.logger.info(f"[NewSlidesGenerator] Slide {slide_num} completed successfully")
                    else:
                        raise RuntimeError(f"Slide {slide_num} validation failed")

                except Exception as e:
                    self.logger.error(f"[NewSlidesGenerator] Failed to create slide {slide_num}: {e}")
                    # Save error details to knowledge base for debugging
                    self.save_slide_error_to_file(slide_num, str(e), outline_slides[i] if i < len(outline_slides) else {})
                    # Continue with next slide rather than failing entire presentation
                    self.runtime_log(f"Slide {slide_num} failed, continuing with next slide")
                    continue

            self.runtime_log("Presentation creation completed")
            self.logger.info(f"[NewSlidesGenerator] Presentation created successfully: {output_path}")

            return output_path

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Incremental presentation creation failed: {e}")
            raise

    async def create_base_presentation(self, output_path: Path, style_template: Dict[str, Any]):
        """Create empty presentation with basic setup."""
        try:
            global_theme = style_template.get("global_theme", {})

            base_code = f'''
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

# Create base presentation
prs = Presentation()

# Save base presentation
prs.save(r"{output_path}")
'''

            self.logger.info(f"[NewSlidesGenerator] Creating base presentation at: {output_path}")
            await self.execute_slide_code(base_code, "base")

            if not output_path.exists():
                raise RuntimeError("Base presentation file was not created")

            self.logger.info("[NewSlidesGenerator] Base presentation created successfully")

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Base presentation creation failed: {e}")
            raise

    async def generate_slide_code(
        self, slide_index: int, outline_plan: Dict[str, Any],
        style_template: Dict[str, Any], presentation_path: Path
    ) -> str:
        """Generate code for a single slide using both outline and template data with retry logic."""
        try:
            # Get slide data from both sources
            outline_slides = outline_plan.get("slides", [])
            template_slides = style_template.get("slides", [])

            if slide_index >= len(outline_slides):
                raise IndexError(f"Slide index {slide_index} not found in outline")

            outline_slide = outline_slides[slide_index]

            # Use template slide if available, otherwise use default styling
            if slide_index < len(template_slides):
                template_slide = template_slides[slide_index]
            else:
                template_slide = {"styling": {}}

            # Get relevant python-pptx code examples from RAG
            slide_context = f"slide {outline_slide.get('layout_type', 'content')} {outline_slide.get('content_type', 'text')}"
            pptx_examples = get_pptx_code_examples_text(slide_context, k=4)

            # Format slide data as markdown instead of raw JSON
            slide_content_md = self._format_slide_content_as_markdown(outline_slide)
            slide_styling_md = self._format_slide_styling_as_markdown(template_slide, style_template)

            # Prepare common input data
            base_input_data = {
                "slide_content": slide_content_md,
                "slide_styling": slide_styling_md,
                "presentation_path": str(presentation_path),
                "examples": pptx_examples
            }

            slide_number = slide_index + 1
            max_retries = 2

            for attempt in range(max_retries + 1):  # 0, 1, 2 (3 total attempts)
                try:
                    if attempt == 0:
                        # First attempt: use original prompt
                        self.logger.info(f"[NewSlidesGenerator] Generating code for slide {slide_number}")
                        chain = slide_code_prompt | slide_code_model | StrOutputParser()
                        code_text = await self._invoke_chain(chain, base_input_data)
                    else:
                        # Retry attempts: use fixing prompt with error feedback
                        self.runtime_log(f"Code generation failed for slide {slide_number}, retrying (attempt {attempt + 1})...")
                        self.logger.info(f"[NewSlidesGenerator] Retrying code generation for slide {slide_number} (attempt {attempt + 1})")

                        fix_input_data = {
                            **base_input_data,
                            "failed_code": failed_code,
                            "error_message": error_message
                        }

                        chain = slide_code_fix_prompt | slide_code_model | StrOutputParser()
                        code_text = await self._invoke_chain(chain, fix_input_data)

                    self.logger.info(f"[NewSlidesGenerator] Raw code response: {code_text[:200]}...")

                    # Clean up the code response
                    cleaned_code = self._clean_python_code(code_text)
                    self.logger.info(f"[NewSlidesGenerator] Cleaned code: {cleaned_code[:200]}...")

                    # Test the code by executing it
                    success, error_msg = await self.execute_slide_code(cleaned_code, str(slide_number))

                    if success:
                        # Code executed successfully
                        self.logger.info(f"[NewSlidesGenerator] Generated slide code ({len(cleaned_code)} chars)")
                        self.save_slide_code_to_file(slide_number, cleaned_code, outline_slide, template_slide)

                        if attempt > 0:
                            self.runtime_log(f"Code generation succeeded for slide {slide_number} after {attempt + 1} attempts")

                        return cleaned_code
                    else:
                        # Code execution failed, save failed attempt for debugging
                        self.save_failed_slide_code_to_file(slide_number, cleaned_code, error_msg, attempt + 1, outline_slide, template_slide)

                        # Prepare for retry
                        failed_code = cleaned_code
                        error_message = error_msg
                        self.logger.warning(f"[NewSlidesGenerator] Slide {slide_number} code execution failed (attempt {attempt + 1}): {error_msg}")

                        if attempt == max_retries:
                            # Final attempt failed
                            self.runtime_log(f"Code generation failed for slide {slide_number} after {max_retries + 1} attempts")
                            raise RuntimeError(f"Slide {slide_number} code generation failed after {max_retries + 1} attempts. Final error: {error_msg}")

                except Exception as e:
                    if attempt == max_retries:
                        # Final attempt failed with exception
                        self.logger.error(f"[NewSlidesGenerator] Slide code generation failed for slide {slide_number} after {max_retries + 1} attempts: {e}")
                        raise
                    else:
                        # Save failed attempt if we have code
                        if 'cleaned_code' in locals():
                            self.save_failed_slide_code_to_file(slide_number, cleaned_code, str(e), attempt + 1, outline_slide, template_slide)

                        # Prepare for retry
                        failed_code = getattr(e, 'failed_code', locals().get('cleaned_code', ''))
                        error_message = str(e)
                        self.logger.warning(f"[NewSlidesGenerator] Slide {slide_number} generation failed (attempt {attempt + 1}): {e}")

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Slide code generation failed for slide {slide_index}: {e}")
            raise

    def _format_slide_content_as_markdown(self, outline_slide: dict) -> str:
        """Format slide content data using detailed content from enhanced outline."""
        slide_number = outline_slide.get("slide_number", "Unknown")
        layout_type = outline_slide.get("layout_type", "Unknown")
        title = outline_slide.get("title", "No title")
        content_type = outline_slide.get("content_type", "Unknown")

        # Use detailed content if available, fallback to content_outline
        detailed_content = outline_slide.get("detailed_content", {})
        if detailed_content:
            bullet_points = detailed_content.get("bullet_points", [])
            main_text = detailed_content.get("main_text", "")

            content_md = f"""**Slide {slide_number} Content:**
- **Layout Type:** {layout_type}
- **Title:** {title}
- **Content Type:** {content_type}

**Bullet Points:**
{chr(10).join(f"• {point}" for point in bullet_points)}

**Main Text:** {main_text}"""
        else:
            # Fallback to content_outline if detailed_content is missing
            content_outline = outline_slide.get("content_outline", "No content")
            content_md = f"""**Slide {slide_number} Content:**
- **Layout Type:** {layout_type}
- **Title:** {title}
- **Content Type:** {content_type}
- **Content:** {content_outline}"""

        return content_md

    def _format_slide_styling_as_markdown(self, template_slide: dict, style_template: dict) -> str:
        """Format slide styling data as readable markdown including global theme and visual enhancements."""
        styling = template_slide.get("styling", {})
        global_theme = style_template.get("global_theme", {})

        if not styling and not global_theme:
            logger.info(f"[NewSlidesGenerator] No styling found for slide {template_slide}")
            return "**Slide Styling:** Default styling (no custom styling specified)"

        # Format global theme information
        global_items = []
        if global_theme:
            for key, value in global_theme.items():
                readable_key = key.replace("_", " ").title()
                global_items.append(f"- **{readable_key}:** {value}")

        # Format slide-specific styling
        slide_items = []
        if styling:
            for key, value in styling.items():
                readable_key = key.replace("_", " ").title()
                slide_items.append(f"- **{readable_key}:** {value}")

        # Combine global and slide-specific styling
        sections = []

        if global_items:
            global_text = "\n".join(global_items)
            sections.append(f"""**Global Theme:**
{global_text}""")

        if slide_items:
            slide_text = "\n".join(slide_items)
            sections.append(f"""**Slide-Specific Styling:**
{slide_text}""")

        if not sections:
            return "**Slide Styling:** Default styling"

        return "\n\n".join(sections)

    async def execute_slide_code(self, code: str, slide_identifier: str) -> tuple[bool, str]:
        """Execute slide creation code using subprocess.

        Returns:
            tuple[bool, str]: (success, error_message)
        """
        try:
            # Create temporary Python file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
                temp_file.write(code)
                temp_file_path = temp_file.name

            self.logger.info(f"[NewSlidesGenerator] Executing code for slide {slide_identifier}")

            # Execute the Python code in subprocess
            result = subprocess.run(
                ["python", temp_file_path],
                capture_output=True,
                text=True,
                timeout=30,  # Shorter timeout for individual slides
                cwd=str(get_public_slides_dir(self.chat_id))
            )

            # Clean up temporary file
            Path(temp_file_path).unlink(missing_ok=True)

            if result.returncode != 0:
                error_msg = result.stderr.strip() if result.stderr else "Unknown execution error"
                self.logger.error(f"[NewSlidesGenerator] Slide {slide_identifier} execution failed: {error_msg}")
                return False, error_msg

            self.logger.info(f"[NewSlidesGenerator] Slide {slide_identifier} code executed successfully")
            return True, ""

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"[NewSlidesGenerator] Slide {slide_identifier} execution failed: {error_msg}")
            return False, error_msg

    async def validate_slide_created(self, presentation_path: Path, slide_number: int) -> bool:
        """Validate that a slide was successfully added to the presentation."""
        try:
            if not presentation_path.exists():
                return False

            # Simple validation - check file size increased
            file_size = presentation_path.stat().st_size
            if file_size < 1000:  # Less than 1KB is likely corrupted
                return False

            self.logger.info(f"[NewSlidesGenerator] Slide {slide_number} validation passed (size: {file_size} bytes)")
            return True

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Slide {slide_number} validation failed: {e}")
            return False

    #──────────────────────────────────────────────────
    #                         FILE SAVING METHODS
    #──────────────────────────────────────────────────

    def save_outline_to_md_file(self, subtask_id: str, outline_plan: Dict[str, Any]):
        """Save the enhanced outline with detailed content to a markdown file for audit purposes."""
        try:
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"slides_enhanced_outline_{subtask_id}_{timestamp}.md"
            file_path = knowledge_base_dir / filename

            # Format enhanced outline as readable markdown
            content_md = f"# Enhanced Slides Outline with Detailed Content\n\n"
            content_md += f"**Presentation:** {outline_plan.get('title', 'Unknown')}\n"
            content_md += f"**Subtitle:** {outline_plan.get('subtitle', 'N/A')}\n"
            content_md += f"**Total Slides:** {outline_plan.get('total_slides', len(outline_plan.get('slides', [])))}\n\n"

            for slide in outline_plan.get("slides", []):
                slide_num = slide.get("slide_number", "Unknown")
                title = slide.get("title", "No title")
                layout_type = slide.get("layout_type", "Unknown")
                content_type = slide.get("content_type", "Unknown")
                content_outline = slide.get("content_outline", "No content")
                detailed_content = slide.get("detailed_content", {})

                content_md += f"## Slide {slide_num}: {title}\n\n"
                content_md += f"- **Layout Type:** {layout_type}\n"
                content_md += f"- **Content Type:** {content_type}\n"
                content_md += f"- **Brief Outline:** {content_outline}\n\n"

                if detailed_content:
                    bullet_points = detailed_content.get("bullet_points", [])
                    main_text = detailed_content.get("main_text", "")

                    if bullet_points:
                        content_md += "**Detailed Content:**\n"
                        for point in bullet_points:
                            content_md += f"- {point}\n"
                        content_md += "\n"

                    if main_text:
                        content_md += f"**Additional Text:** {main_text}\n\n"
                else:
                    content_md += "*No detailed content generated*\n\n"

                content_md += "---\n\n"

            # Also include raw JSON for reference
            outline_json = json.dumps(outline_plan, ensure_ascii=False, indent=2)
            content_md += f"\n## Raw JSON Structure\n\n```json\n{outline_json}\n```\n"

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content_md)

            self.logger.info(f"[NewSlidesGenerator] Enhanced outline saved to: {file_path}")

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save enhanced outline: {e}")

    def save_style_template_to_md_file(self, subtask_id: str, style_template: Dict[str, Any]):
        """Save the style template JSON to a markdown file for audit purposes."""
        try:
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"slides_style_template_{subtask_id}_{timestamp}.md"
            file_path = knowledge_base_dir / filename

            template_json = json.dumps(style_template, ensure_ascii=False, indent=2)
            content = f"# Slides Style Template\n\n```json\n{template_json}\n```\n"

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)

            self.logger.info(f"[NewSlidesGenerator] Style template saved to: {file_path}")

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save style template: {e}")

    def save_pptx_code_to_file(self, subtask_id: str, pptx_code: str):
        """Save the generated python-pptx code to a file for audit purposes."""
        try:
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"slides_pptx_code_{subtask_id}_{timestamp}.py"
            file_path = knowledge_base_dir / filename

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(pptx_code)

            self.logger.info(f"[NewSlidesGenerator] PPTX code saved to: {file_path}")

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save PPTX code: {e}")

    def save_slide_code_to_file(self, slide_number: int, slide_code: str, outline_slide: dict, template_slide: dict) -> str:
        """Save individual slide generation code to knowledge base for debugging."""
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)
            file_path = knowledge_base_dir / f"slide_{slide_number}_code_{timestamp}.py"

            # Save only the executable code without any metadata
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(slide_code)

            self.logger.info(f"[NewSlidesGenerator] Slide {slide_number} code saved to: {file_path}")
            return str(file_path)

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save slide {slide_number} code: {e}")
            return ""

    def save_failed_slide_code_to_file(self, slide_number: int, failed_code: str, error_message: str, attempt_number: int, outline_slide: dict, template_slide: dict) -> str:
        """Save failed slide generation code attempt to knowledge base for debugging."""
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)
            file_path = knowledge_base_dir / f"slide_{slide_number}_failed_attempt_{attempt_number}_{timestamp}.md"

            # Create comprehensive failed attempt report
            failed_content = f"""# Slide {slide_number} Failed Code Attempt {attempt_number}

**Error Message:** {error_message}

**Slide Number:** {slide_number}
**Attempt Number:** {attempt_number}
**Timestamp:** {datetime.now(timezone.utc).isoformat()}

## Failed Code
```python
{failed_code}
```

## Slide Content (from outline)
```json
{json.dumps(outline_slide, indent=2)}
```

## Slide Styling (from template)
```json
{json.dumps(template_slide, indent=2)}
```

---

This code failed to execute during slide generation. The error message above shows what went wrong. This information is saved for debugging and improving the code generation prompts.
"""

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(failed_content)

            self.logger.info(f"[NewSlidesGenerator] Slide {slide_number} failed attempt {attempt_number} saved to: {file_path}")
            return str(file_path)

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save slide {slide_number} failed attempt: {e}")
            return ""

    def save_slide_error_to_file(self, slide_number: int, error_message: str, outline_slide: dict) -> str:
        """Save slide generation error details to knowledge base for debugging."""
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)
            file_path = knowledge_base_dir / f"slide_{slide_number}_error_{timestamp}.md"

            # Create comprehensive error report
            error_content = f"""# Slide {slide_number} Generation Error

**Error:** {error_message}

**Slide Number:** {slide_number}

**Slide Content (from outline):**
```json
{json.dumps(outline_slide, indent=2)}
```

**Timestamp:** {datetime.now(timezone.utc).isoformat()}

---

This error occurred during slide generation. Check the corresponding slide code file for the generated code that caused this error.
"""

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(error_content)

            self.logger.info(f"[NewSlidesGenerator] Slide {slide_number} error saved to: {file_path}")
            return str(file_path)

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save slide {slide_number} error: {e}")
            return ""

    def save_combined_results_to_md_file(self, subtask_id: str, content: str) -> Path:
        """Save the combined results to a markdown file."""
        try:
            knowledge_base_dir = get_knowledge_base_dir(self.chat_id)
            knowledge_base_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"slides_results_{subtask_id}_{timestamp}.md"
            file_path = knowledge_base_dir / filename

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)

            self.logger.info(f"[NewSlidesGenerator] Results saved to: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[NewSlidesGenerator] Failed to save results: {e}")
            # Return a fallback path
            return knowledge_base_dir / f"slides_results_{subtask_id}_fallback.md"
